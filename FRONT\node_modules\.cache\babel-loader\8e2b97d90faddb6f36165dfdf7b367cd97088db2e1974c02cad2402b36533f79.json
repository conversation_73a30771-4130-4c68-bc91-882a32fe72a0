{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NEW_LMS_FIXING\\\\FRONT\\\\src\\\\pages\\\\user\\\\feed\\\\FeedPost.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useCallback, useMemo } from 'react';\nimport { Icon } from '@iconify/react';\nimport DefaultProfile from '../../../assets/images/profile/default-profile.png';\nimport { createPost } from '../../../services/feedServices';\nimport { toast } from 'react-toastify';\n\n// Move MediaUploadButton outside to prevent recreation on every render\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MediaUploadButton = /*#__PURE__*/React.memo(_c = ({\n  icon,\n  text,\n  onClick,\n  buttonStyle\n}) => /*#__PURE__*/_jsxDEV(\"button\", {\n  className: \"btn border text-muted btn-sm\",\n  style: buttonStyle,\n  onClick: onClick,\n  type: \"button\",\n  children: [/*#__PURE__*/_jsxDEV(Icon, {\n    icon: icon,\n    className: \"me-1 d-none d-md-inline\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Icon, {\n    icon: icon,\n    className: \"d-md-none\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n    className: \"d-none d-md-inline\",\n    children: text\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 9,\n  columnNumber: 3\n}, this));\n_c2 = MediaUploadButton;\nconst FeedPost = ({\n  onPostSubmit,\n  userProfile\n}) => {\n  _s();\n  const [newPost, setNewPost] = useState('');\n  const [newPostMedia, setNewPostMedia] = useState(null);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const MAX_CHARACTERS = 1000;\n\n  // Refs for file inputs\n  const imageInputRef = useRef(null);\n  const videoInputRef = useRef(null);\n\n  // Memoized styles to prevent re-renders\n  const buttonStyle = useMemo(() => ({\n    backgroundColor: 'transparent',\n    borderColor: '#dee2e6'\n  }), []);\n  const postButtonStyle = useMemo(() => ({\n    borderRadius: '20px',\n    fontWeight: '500',\n    transition: 'all 0.2s ease',\n    minWidth: '100px'\n  }), []);\n  const mediaStyle = useMemo(() => ({\n    width: '100%',\n    height: 'auto',\n    maxHeight: '400px',\n    objectFit: 'cover',\n    borderRadius: '8px'\n  }), []);\n\n  // Event handlers\n  const handleMediaUpload = useCallback((e, type) => {\n    const file = e.target.files[0];\n    if (file) {\n      setNewPostMedia({\n        type,\n        url: URL.createObjectURL(file),\n        file\n      });\n    }\n  }, []);\n  const handleImageUpload = useCallback(e => {\n    handleMediaUpload(e, 'image');\n  }, [handleMediaUpload]);\n  const handleVideoUpload = useCallback(e => {\n    handleMediaUpload(e, 'video');\n  }, [handleMediaUpload]);\n  const handleImageButtonClick = useCallback(() => {\n    if (imageInputRef.current) {\n      imageInputRef.current.click();\n    }\n  }, []);\n  const handleVideoButtonClick = useCallback(() => {\n    if (videoInputRef.current) {\n      videoInputRef.current.click();\n    }\n  }, []);\n\n  // Memoized textarea change handler\n  const handleTextareaChange = useCallback(e => {\n    setNewPost(e.target.value);\n  }, []);\n\n  // Memoized computed values\n  const hasContent = useMemo(() => newPost.trim() || newPostMedia, [newPost, newPostMedia]);\n  const isPostButtonEnabled = useMemo(() => hasContent && !isSubmitting, [hasContent, isSubmitting]);\n  const postButtonClass = useMemo(() => `btn px-4 w-auto py-2 ${isPostButtonEnabled ? 'btn-primary' : 'btn-secondary'}`, [isPostButtonEnabled]);\n  const handleSubmitPost = async () => {\n    if (!newPost.trim() && !newPostMedia) {\n      console.error('Please add some content or media to your post');\n      return;\n    }\n    if (newPost.length > MAX_CHARACTERS) {\n      console.error(`Post content cannot exceed ${MAX_CHARACTERS} characters`);\n      return;\n    }\n    setIsSubmitting(true);\n    try {\n      const postData = {\n        description: newPost.trim(),\n        media: newPostMedia\n      };\n      const response = await createPost(postData);\n      if (response.success) {\n        console.log('Post created successfully!');\n        setNewPost('');\n        setNewPostMedia(null);\n\n        // Call the parent callback to refresh the feed\n        if (onPostSubmit) {\n          onPostSubmit(response.data.post);\n        }\n      } else {\n        var _response$data;\n        console.error(((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.error_msg) || 'Failed to create post');\n      }\n    } catch (error) {\n      console.error('Error creating post:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  // Render functions\n  const renderMedia = media => {\n    if (media.type === 'image') {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"position-relative\",\n        style: {\n          width: '100%',\n          maxHeight: '400px',\n          overflow: 'hidden',\n          borderRadius: '8px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: media.url,\n          className: \"img-fluid\",\n          alt: \"Post media\",\n          style: {\n            width: '100%',\n            height: 'auto',\n            maxHeight: '400px',\n            display: 'block',\n            objectFit: 'contain'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this);\n    } else if (media.type === 'video') {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"position-relative\",\n        style: {\n          width: '100%',\n          maxHeight: '400px',\n          overflow: 'hidden',\n          borderRadius: '8px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"video\", {\n          className: \"img-fluid\",\n          controls: true,\n          style: {\n            width: '100%',\n            height: 'auto',\n            maxHeight: '400px',\n            objectFit: 'cover',\n            display: 'block'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"source\", {\n            src: media.url,\n            type: \"video/mp4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this), \"Your browser does not support the video tag.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"card mb-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card-body\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.profile_pic_url) || DefaultProfile,\n          className: \"rounded-circle me-3\",\n          alt: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.name) || \"Profile\",\n          style: {\n            width: '40px',\n            height: '40px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-grow-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n            className: \"form-control border-0\",\n            rows: \"3\",\n            placeholder: \"What's on your mind?\",\n            value: newPost,\n            onChange: handleTextareaChange,\n            maxLength: MAX_CHARACTERS\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-end mt-2\",\n            children: /*#__PURE__*/_jsxDEV(\"small\", {\n              className: newPost.length > MAX_CHARACTERS * 0.9 ? 'text-warning' : 'text-muted',\n              children: [newPost.length, \"/\", MAX_CHARACTERS, \" characters\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this), newPostMedia && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-3\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"position-relative\",\n          children: [renderMedia(newPostMedia), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-sm btn-danger position-absolute top-0 end-0 m-2 w-auto\",\n            onClick: () => setNewPostMedia(null),\n            style: {\n              zIndex: 10\n            },\n            children: /*#__PURE__*/_jsxDEV(Icon, {\n              icon: \"mdi:close\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        ref: imageInputRef,\n        type: \"file\",\n        accept: \"image/*\",\n        style: {\n          display: 'none'\n        },\n        onChange: handleImageUpload\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        ref: videoInputRef,\n        type: \"file\",\n        accept: \"video/*\",\n        style: {\n          display: 'none'\n        },\n        onChange: handleVideoUpload\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-between align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(MediaUploadButton, {\n            icon: \"mdi:camera\",\n            text: \"Photo\",\n            onClick: handleImageButtonClick,\n            buttonStyle: buttonStyle\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MediaUploadButton, {\n            icon: \"mdi:video\",\n            text: \"Video\",\n            onClick: handleVideoButtonClick,\n            buttonStyle: buttonStyle\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: postButtonClass,\n          style: postButtonStyle,\n          onClick: handleSubmitPost,\n          disabled: !isPostButtonEnabled,\n          children: isSubmitting ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner-border spinner-border-sm me-2\",\n              role: \"status\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"visually-hidden\",\n                children: \"Loading...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 17\n            }, this), \"Posting...\"]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              icon: \"mdi:send\",\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 17\n            }, this), \"Post\"]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 179,\n    columnNumber: 5\n  }, this);\n};\n_s(FeedPost, \"Ppr9LgMhYWtrGemqSgl08W/MGvI=\");\n_c3 = FeedPost;\nexport default FeedPost;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"MediaUploadButton$React.memo\");\n$RefreshReg$(_c2, \"MediaUploadButton\");\n$RefreshReg$(_c3, \"FeedPost\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useCallback", "useMemo", "Icon", "DefaultProfile", "createPost", "toast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MediaUploadButton", "memo", "_c", "icon", "text", "onClick", "buttonStyle", "className", "style", "type", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c2", "FeedPost", "onPostSubmit", "userProfile", "_s", "newPost", "setNewPost", "newPostMedia", "setNewPostMedia", "isSubmitting", "setIsSubmitting", "MAX_CHARACTERS", "imageInputRef", "videoInputRef", "backgroundColor", "borderColor", "postButtonStyle", "borderRadius", "fontWeight", "transition", "min<PERSON><PERSON><PERSON>", "mediaStyle", "width", "height", "maxHeight", "objectFit", "handleMediaUpload", "e", "file", "target", "files", "url", "URL", "createObjectURL", "handleImageUpload", "handleVideoUpload", "handleImageButtonClick", "current", "click", "handleVideoButtonClick", "handleTextareaChange", "value", "<PERSON><PERSON><PERSON><PERSON>", "trim", "isPostButtonEnabled", "postButtonClass", "handleSubmitPost", "console", "error", "length", "postData", "description", "media", "response", "success", "log", "data", "post", "_response$data", "error_msg", "renderMedia", "overflow", "src", "alt", "display", "controls", "profile_pic_url", "name", "rows", "placeholder", "onChange", "max<PERSON><PERSON><PERSON>", "zIndex", "ref", "accept", "disabled", "role", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/pages/user/feed/FeedPost.jsx"], "sourcesContent": ["import React, { useState, useRef, useCallback, useMemo } from 'react'\r\nimport { Icon } from '@iconify/react'\r\nimport DefaultProfile from '../../../assets/images/profile/default-profile.png'\r\nimport { createPost } from '../../../services/feedServices'\r\nimport { toast } from 'react-toastify'\r\n\r\n// Move MediaUploadButton outside to prevent recreation on every render\r\nconst MediaUploadButton = React.memo(({ icon, text, onClick, buttonStyle }) => (\r\n  <button\r\n    className=\"btn border text-muted btn-sm\"\r\n    style={buttonStyle}\r\n    onClick={onClick}\r\n    type=\"button\"\r\n  >\r\n    <Icon icon={icon} className=\"me-1 d-none d-md-inline\" />\r\n    <Icon icon={icon} className=\"d-md-none\" />\r\n    <span className=\"d-none d-md-inline\">{text}</span>\r\n  </button>\r\n));\r\n\r\nconst FeedPost = ({ onPostSubmit, userProfile }) => {\r\n  const [newPost, setNewPost] = useState('');\r\n  const [newPostMedia, setNewPostMedia] = useState(null);\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const MAX_CHARACTERS = 1000;\r\n\r\n  // Refs for file inputs\r\n  const imageInputRef = useRef(null);\r\n  const videoInputRef = useRef(null);\r\n\r\n  // Memoized styles to prevent re-renders\r\n  const buttonStyle = useMemo(() => ({\r\n    backgroundColor: 'transparent',\r\n    borderColor: '#dee2e6'\r\n  }), []);\r\n\r\n  const postButtonStyle = useMemo(() => ({\r\n    borderRadius: '20px',\r\n    fontWeight: '500',\r\n    transition: 'all 0.2s ease',\r\n    minWidth: '100px'\r\n  }), []);\r\n\r\n  const mediaStyle = useMemo(() => ({\r\n    width: '100%',\r\n    height: 'auto',\r\n    maxHeight: '400px',\r\n    objectFit: 'cover',\r\n    borderRadius: '8px'\r\n  }), []);\r\n\r\n  // Event handlers\r\n  const handleMediaUpload = useCallback((e, type) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      setNewPostMedia({\r\n        type,\r\n        url: URL.createObjectURL(file),\r\n        file\r\n      });\r\n    }\r\n  }, []);\r\n\r\n  const handleImageUpload = useCallback((e) => {\r\n    handleMediaUpload(e, 'image');\r\n  }, [handleMediaUpload]);\r\n\r\n  const handleVideoUpload = useCallback((e) => {\r\n    handleMediaUpload(e, 'video');\r\n  }, [handleMediaUpload]);\r\n\r\n  const handleImageButtonClick = useCallback(() => {\r\n    if (imageInputRef.current) {\r\n      imageInputRef.current.click();\r\n    }\r\n  }, []);\r\n\r\n  const handleVideoButtonClick = useCallback(() => {\r\n    if (videoInputRef.current) {\r\n      videoInputRef.current.click();\r\n    }\r\n  }, []);\r\n\r\n  // Memoized textarea change handler\r\n  const handleTextareaChange = useCallback((e) => {\r\n    setNewPost(e.target.value);\r\n  }, []);\r\n\r\n  // Memoized computed values\r\n  const hasContent = useMemo(() => newPost.trim() || newPostMedia, [newPost, newPostMedia]);\r\n  const isPostButtonEnabled = useMemo(() => hasContent && !isSubmitting, [hasContent, isSubmitting]);\r\n  const postButtonClass = useMemo(() =>\r\n    `btn px-4 w-auto py-2 ${isPostButtonEnabled ? 'btn-primary' : 'btn-secondary'}`,\r\n    [isPostButtonEnabled]\r\n  );\r\n\r\n  const handleSubmitPost = async () => {\r\n    if (!newPost.trim() && !newPostMedia) {\r\n      console.error('Please add some content or media to your post');\r\n      return;\r\n    }\r\n\r\n    if (newPost.length > MAX_CHARACTERS) {\r\n      console.error(`Post content cannot exceed ${MAX_CHARACTERS} characters`);\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n    try {\r\n      const postData = {\r\n        description: newPost.trim(),\r\n        media: newPostMedia\r\n      };\r\n\r\n      const response = await createPost(postData);\r\n\r\n      if (response.success) {\r\n        console.log('Post created successfully!');\r\n        setNewPost('');\r\n        setNewPostMedia(null);\r\n\r\n        // Call the parent callback to refresh the feed\r\n        if (onPostSubmit) {\r\n          onPostSubmit(response.data.post);\r\n        }\r\n      } else {\r\n        console.error(response.data?.error_msg || 'Failed to create post');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error creating post:', error);\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Render functions\r\n  const renderMedia = (media) => {\r\n    if (media.type === 'image') {\r\n      return (\r\n        <div className=\"position-relative\" style={{ width: '100%', maxHeight: '400px', overflow: 'hidden', borderRadius: '8px' }}>\r\n          <img \r\n            src={media.url} \r\n            className=\"img-fluid\" \r\n            alt=\"Post media\" \r\n            style={{\r\n              width: '100%',\r\n              height: 'auto',\r\n              maxHeight: '400px',\r\n              display: 'block',\r\n              objectFit: 'contain'\r\n            }} \r\n          />\r\n        </div>\r\n      );\r\n    } else if (media.type === 'video') {\r\n      return (\r\n        <div className=\"position-relative\" style={{ width: '100%', maxHeight: '400px', overflow: 'hidden', borderRadius: '8px' }}>\r\n          <video \r\n            className=\"img-fluid\" \r\n            controls \r\n            style={{\r\n              width: '100%',\r\n              height: 'auto',\r\n              maxHeight: '400px',\r\n              objectFit: 'cover',\r\n              display: 'block'\r\n            }}\r\n          >\r\n            <source src={media.url} type=\"video/mp4\" />\r\n            Your browser does not support the video tag.\r\n          </video>\r\n        </div>\r\n      );\r\n    }\r\n    return null;\r\n  };\r\n\r\n  return (\r\n    <div className=\"card mb-4\">\r\n      <div className=\"card-body\">\r\n        <div className=\"d-flex mb-3\">\r\n          <img \r\n            src={userProfile?.profile_pic_url || DefaultProfile} \r\n            className=\"rounded-circle me-3\" \r\n            alt={userProfile?.name || \"Profile\"} \r\n            style={{width: '40px', height: '40px'}} \r\n          />\r\n          <div className=\"flex-grow-1\">\r\n            <textarea\r\n              className=\"form-control border-0\"\r\n              rows=\"3\"\r\n              placeholder=\"What's on your mind?\"\r\n              value={newPost}\r\n              onChange={handleTextareaChange}\r\n              maxLength={MAX_CHARACTERS}\r\n            />\r\n            <div className=\"d-flex justify-content-end mt-2\">\r\n              <small className={newPost.length > MAX_CHARACTERS * 0.9 ? 'text-warning' : 'text-muted'}>\r\n                {newPost.length}/{MAX_CHARACTERS} characters\r\n              </small>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Media Preview */}\r\n        {newPostMedia && (\r\n          <div className=\"mb-3\">\r\n            <div className=\"position-relative\">\r\n              {renderMedia(newPostMedia)}\r\n              <button \r\n                className=\"btn btn-sm btn-danger position-absolute top-0 end-0 m-2 w-auto\"\r\n                onClick={() => setNewPostMedia(null)}\r\n                style={{ zIndex: 10 }}\r\n              >\r\n                <Icon icon=\"mdi:close\" />\r\n              </button>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Hidden file inputs */}\r\n        <input\r\n          ref={imageInputRef}\r\n          type=\"file\"\r\n          accept=\"image/*\"\r\n          style={{ display: 'none' }}\r\n          onChange={handleImageUpload}\r\n        />\r\n        <input\r\n          ref={videoInputRef}\r\n          type=\"file\"\r\n          accept=\"video/*\"\r\n            style={{ display: 'none' }}\r\n          onChange={handleVideoUpload}\r\n        />\r\n\r\n        {/* Action Buttons */}\r\n        <div className=\"d-flex justify-content-between align-items-center\">\r\n          <div className=\"d-flex gap-2\">\r\n            <MediaUploadButton\r\n              icon=\"mdi:camera\"\r\n              text=\"Photo\"\r\n              onClick={handleImageButtonClick}\r\n              buttonStyle={buttonStyle}\r\n            />\r\n            <MediaUploadButton\r\n              icon=\"mdi:video\"\r\n              text=\"Video\"\r\n              onClick={handleVideoButtonClick}\r\n              buttonStyle={buttonStyle}\r\n            />\r\n          </div>\r\n          <button\r\n            className={postButtonClass}\r\n            style={postButtonStyle}\r\n            onClick={handleSubmitPost}\r\n            disabled={!isPostButtonEnabled}\r\n          >\r\n            {isSubmitting ? (\r\n              <>\r\n                <div className=\"spinner-border spinner-border-sm me-2\" role=\"status\">\r\n                  <span className=\"visually-hidden\">Loading...</span>\r\n                </div>\r\n                Posting...\r\n              </>\r\n            ) : (\r\n              <>\r\n                <Icon icon=\"mdi:send\" className=\"me-2\" />\r\n                Post\r\n              </>\r\n            )}\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FeedPost;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACrE,SAASC,IAAI,QAAQ,gBAAgB;AACrC,OAAOC,cAAc,MAAM,oDAAoD;AAC/E,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,KAAK,QAAQ,gBAAgB;;AAEtC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,iBAAiB,gBAAGb,KAAK,CAACc,IAAI,CAAAC,EAAA,GAACA,CAAC;EAAEC,IAAI;EAAEC,IAAI;EAAEC,OAAO;EAAEC;AAAY,CAAC,kBACxET,OAAA;EACEU,SAAS,EAAC,8BAA8B;EACxCC,KAAK,EAAEF,WAAY;EACnBD,OAAO,EAAEA,OAAQ;EACjBI,IAAI,EAAC,QAAQ;EAAAC,QAAA,gBAEbb,OAAA,CAACL,IAAI;IAACW,IAAI,EAAEA,IAAK;IAACI,SAAS,EAAC;EAAyB;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eACxDjB,OAAA,CAACL,IAAI;IAACW,IAAI,EAAEA,IAAK;IAACI,SAAS,EAAC;EAAW;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eAC1CjB,OAAA;IAAMU,SAAS,EAAC,oBAAoB;IAAAG,QAAA,EAAEN;EAAI;IAAAO,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAO,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC5C,CACT,CAAC;AAACC,GAAA,GAXGf,iBAAiB;AAavB,MAAMgB,QAAQ,GAAGA,CAAC;EAAEC,YAAY;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EAClD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACkC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAMsC,cAAc,GAAG,IAAI;;EAE3B;EACA,MAAMC,aAAa,GAAGtC,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMuC,aAAa,GAAGvC,MAAM,CAAC,IAAI,CAAC;;EAElC;EACA,MAAMiB,WAAW,GAAGf,OAAO,CAAC,OAAO;IACjCsC,eAAe,EAAE,aAAa;IAC9BC,WAAW,EAAE;EACf,CAAC,CAAC,EAAE,EAAE,CAAC;EAEP,MAAMC,eAAe,GAAGxC,OAAO,CAAC,OAAO;IACrCyC,YAAY,EAAE,MAAM;IACpBC,UAAU,EAAE,KAAK;IACjBC,UAAU,EAAE,eAAe;IAC3BC,QAAQ,EAAE;EACZ,CAAC,CAAC,EAAE,EAAE,CAAC;EAEP,MAAMC,UAAU,GAAG7C,OAAO,CAAC,OAAO;IAChC8C,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdC,SAAS,EAAE,OAAO;IAClBC,SAAS,EAAE,OAAO;IAClBR,YAAY,EAAE;EAChB,CAAC,CAAC,EAAE,EAAE,CAAC;;EAEP;EACA,MAAMS,iBAAiB,GAAGnD,WAAW,CAAC,CAACoD,CAAC,EAAEjC,IAAI,KAAK;IACjD,MAAMkC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAIF,IAAI,EAAE;MACRpB,eAAe,CAAC;QACdd,IAAI;QACJqC,GAAG,EAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;QAC9BA;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMM,iBAAiB,GAAG3D,WAAW,CAAEoD,CAAC,IAAK;IAC3CD,iBAAiB,CAACC,CAAC,EAAE,OAAO,CAAC;EAC/B,CAAC,EAAE,CAACD,iBAAiB,CAAC,CAAC;EAEvB,MAAMS,iBAAiB,GAAG5D,WAAW,CAAEoD,CAAC,IAAK;IAC3CD,iBAAiB,CAACC,CAAC,EAAE,OAAO,CAAC;EAC/B,CAAC,EAAE,CAACD,iBAAiB,CAAC,CAAC;EAEvB,MAAMU,sBAAsB,GAAG7D,WAAW,CAAC,MAAM;IAC/C,IAAIqC,aAAa,CAACyB,OAAO,EAAE;MACzBzB,aAAa,CAACyB,OAAO,CAACC,KAAK,CAAC,CAAC;IAC/B;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,sBAAsB,GAAGhE,WAAW,CAAC,MAAM;IAC/C,IAAIsC,aAAa,CAACwB,OAAO,EAAE;MACzBxB,aAAa,CAACwB,OAAO,CAACC,KAAK,CAAC,CAAC;IAC/B;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAME,oBAAoB,GAAGjE,WAAW,CAAEoD,CAAC,IAAK;IAC9CrB,UAAU,CAACqB,CAAC,CAACE,MAAM,CAACY,KAAK,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,UAAU,GAAGlE,OAAO,CAAC,MAAM6B,OAAO,CAACsC,IAAI,CAAC,CAAC,IAAIpC,YAAY,EAAE,CAACF,OAAO,EAAEE,YAAY,CAAC,CAAC;EACzF,MAAMqC,mBAAmB,GAAGpE,OAAO,CAAC,MAAMkE,UAAU,IAAI,CAACjC,YAAY,EAAE,CAACiC,UAAU,EAAEjC,YAAY,CAAC,CAAC;EAClG,MAAMoC,eAAe,GAAGrE,OAAO,CAAC,MAC9B,wBAAwBoE,mBAAmB,GAAG,aAAa,GAAG,eAAe,EAAE,EAC/E,CAACA,mBAAmB,CACtB,CAAC;EAED,MAAME,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAACzC,OAAO,CAACsC,IAAI,CAAC,CAAC,IAAI,CAACpC,YAAY,EAAE;MACpCwC,OAAO,CAACC,KAAK,CAAC,+CAA+C,CAAC;MAC9D;IACF;IAEA,IAAI3C,OAAO,CAAC4C,MAAM,GAAGtC,cAAc,EAAE;MACnCoC,OAAO,CAACC,KAAK,CAAC,8BAA8BrC,cAAc,aAAa,CAAC;MACxE;IACF;IAEAD,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF,MAAMwC,QAAQ,GAAG;QACfC,WAAW,EAAE9C,OAAO,CAACsC,IAAI,CAAC,CAAC;QAC3BS,KAAK,EAAE7C;MACT,CAAC;MAED,MAAM8C,QAAQ,GAAG,MAAM1E,UAAU,CAACuE,QAAQ,CAAC;MAE3C,IAAIG,QAAQ,CAACC,OAAO,EAAE;QACpBP,OAAO,CAACQ,GAAG,CAAC,4BAA4B,CAAC;QACzCjD,UAAU,CAAC,EAAE,CAAC;QACdE,eAAe,CAAC,IAAI,CAAC;;QAErB;QACA,IAAIN,YAAY,EAAE;UAChBA,YAAY,CAACmD,QAAQ,CAACG,IAAI,CAACC,IAAI,CAAC;QAClC;MACF,CAAC,MAAM;QAAA,IAAAC,cAAA;QACLX,OAAO,CAACC,KAAK,CAAC,EAAAU,cAAA,GAAAL,QAAQ,CAACG,IAAI,cAAAE,cAAA,uBAAbA,cAAA,CAAeC,SAAS,KAAI,uBAAuB,CAAC;MACpE;IACF,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C,CAAC,SAAS;MACRtC,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMkD,WAAW,GAAIR,KAAK,IAAK;IAC7B,IAAIA,KAAK,CAAC1D,IAAI,KAAK,OAAO,EAAE;MAC1B,oBACEZ,OAAA;QAAKU,SAAS,EAAC,mBAAmB;QAACC,KAAK,EAAE;UAAE6B,KAAK,EAAE,MAAM;UAAEE,SAAS,EAAE,OAAO;UAAEqC,QAAQ,EAAE,QAAQ;UAAE5C,YAAY,EAAE;QAAM,CAAE;QAAAtB,QAAA,eACvHb,OAAA;UACEgF,GAAG,EAAEV,KAAK,CAACrB,GAAI;UACfvC,SAAS,EAAC,WAAW;UACrBuE,GAAG,EAAC,YAAY;UAChBtE,KAAK,EAAE;YACL6B,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdC,SAAS,EAAE,OAAO;YAClBwC,OAAO,EAAE,OAAO;YAChBvC,SAAS,EAAE;UACb;QAAE;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAEV,CAAC,MAAM,IAAIqD,KAAK,CAAC1D,IAAI,KAAK,OAAO,EAAE;MACjC,oBACEZ,OAAA;QAAKU,SAAS,EAAC,mBAAmB;QAACC,KAAK,EAAE;UAAE6B,KAAK,EAAE,MAAM;UAAEE,SAAS,EAAE,OAAO;UAAEqC,QAAQ,EAAE,QAAQ;UAAE5C,YAAY,EAAE;QAAM,CAAE;QAAAtB,QAAA,eACvHb,OAAA;UACEU,SAAS,EAAC,WAAW;UACrByE,QAAQ;UACRxE,KAAK,EAAE;YACL6B,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdC,SAAS,EAAE,OAAO;YAClBC,SAAS,EAAE,OAAO;YAClBuC,OAAO,EAAE;UACX,CAAE;UAAArE,QAAA,gBAEFb,OAAA;YAAQgF,GAAG,EAAEV,KAAK,CAACrB,GAAI;YAACrC,IAAI,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gDAE7C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAEV;IACA,OAAO,IAAI;EACb,CAAC;EAED,oBACEjB,OAAA;IAAKU,SAAS,EAAC,WAAW;IAAAG,QAAA,eACxBb,OAAA;MAAKU,SAAS,EAAC,WAAW;MAAAG,QAAA,gBACxBb,OAAA;QAAKU,SAAS,EAAC,aAAa;QAAAG,QAAA,gBAC1Bb,OAAA;UACEgF,GAAG,EAAE,CAAA3D,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE+D,eAAe,KAAIxF,cAAe;UACpDc,SAAS,EAAC,qBAAqB;UAC/BuE,GAAG,EAAE,CAAA5D,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEgE,IAAI,KAAI,SAAU;UACpC1E,KAAK,EAAE;YAAC6B,KAAK,EAAE,MAAM;YAAEC,MAAM,EAAE;UAAM;QAAE;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eACFjB,OAAA;UAAKU,SAAS,EAAC,aAAa;UAAAG,QAAA,gBAC1Bb,OAAA;YACEU,SAAS,EAAC,uBAAuB;YACjC4E,IAAI,EAAC,GAAG;YACRC,WAAW,EAAC,sBAAsB;YAClC5B,KAAK,EAAEpC,OAAQ;YACfiE,QAAQ,EAAE9B,oBAAqB;YAC/B+B,SAAS,EAAE5D;UAAe;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eACFjB,OAAA;YAAKU,SAAS,EAAC,iCAAiC;YAAAG,QAAA,eAC9Cb,OAAA;cAAOU,SAAS,EAAEa,OAAO,CAAC4C,MAAM,GAAGtC,cAAc,GAAG,GAAG,GAAG,cAAc,GAAG,YAAa;cAAAhB,QAAA,GACrFU,OAAO,CAAC4C,MAAM,EAAC,GAAC,EAACtC,cAAc,EAAC,aACnC;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLQ,YAAY,iBACXzB,OAAA;QAAKU,SAAS,EAAC,MAAM;QAAAG,QAAA,eACnBb,OAAA;UAAKU,SAAS,EAAC,mBAAmB;UAAAG,QAAA,GAC/BiE,WAAW,CAACrD,YAAY,CAAC,eAC1BzB,OAAA;YACEU,SAAS,EAAC,gEAAgE;YAC1EF,OAAO,EAAEA,CAAA,KAAMkB,eAAe,CAAC,IAAI,CAAE;YACrCf,KAAK,EAAE;cAAE+E,MAAM,EAAE;YAAG,CAAE;YAAA7E,QAAA,eAEtBb,OAAA,CAACL,IAAI;cAACW,IAAI,EAAC;YAAW;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGDjB,OAAA;QACE2F,GAAG,EAAE7D,aAAc;QACnBlB,IAAI,EAAC,MAAM;QACXgF,MAAM,EAAC,SAAS;QAChBjF,KAAK,EAAE;UAAEuE,OAAO,EAAE;QAAO,CAAE;QAC3BM,QAAQ,EAAEpC;MAAkB;QAAAtC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eACFjB,OAAA;QACE2F,GAAG,EAAE5D,aAAc;QACnBnB,IAAI,EAAC,MAAM;QACXgF,MAAM,EAAC,SAAS;QACdjF,KAAK,EAAE;UAAEuE,OAAO,EAAE;QAAO,CAAE;QAC7BM,QAAQ,EAAEnC;MAAkB;QAAAvC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eAGFjB,OAAA;QAAKU,SAAS,EAAC,mDAAmD;QAAAG,QAAA,gBAChEb,OAAA;UAAKU,SAAS,EAAC,cAAc;UAAAG,QAAA,gBAC3Bb,OAAA,CAACG,iBAAiB;YAChBG,IAAI,EAAC,YAAY;YACjBC,IAAI,EAAC,OAAO;YACZC,OAAO,EAAE8C,sBAAuB;YAChC7C,WAAW,EAAEA;UAAY;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACFjB,OAAA,CAACG,iBAAiB;YAChBG,IAAI,EAAC,WAAW;YAChBC,IAAI,EAAC,OAAO;YACZC,OAAO,EAAEiD,sBAAuB;YAChChD,WAAW,EAAEA;UAAY;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNjB,OAAA;UACEU,SAAS,EAAEqD,eAAgB;UAC3BpD,KAAK,EAAEuB,eAAgB;UACvB1B,OAAO,EAAEwD,gBAAiB;UAC1B6B,QAAQ,EAAE,CAAC/B,mBAAoB;UAAAjD,QAAA,EAE9Bc,YAAY,gBACX3B,OAAA,CAAAE,SAAA;YAAAW,QAAA,gBACEb,OAAA;cAAKU,SAAS,EAAC,uCAAuC;cAACoF,IAAI,EAAC,QAAQ;cAAAjF,QAAA,eAClEb,OAAA;gBAAMU,SAAS,EAAC,iBAAiB;gBAAAG,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,cAER;UAAA,eAAE,CAAC,gBAEHjB,OAAA,CAAAE,SAAA;YAAAW,QAAA,gBACEb,OAAA,CAACL,IAAI;cAACW,IAAI,EAAC,UAAU;cAACI,SAAS,EAAC;YAAM;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,QAE3C;UAAA,eAAE;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACK,EAAA,CAhQIH,QAAQ;AAAA4E,GAAA,GAAR5E,QAAQ;AAkQd,eAAeA,QAAQ;AAAC,IAAAd,EAAA,EAAAa,GAAA,EAAA6E,GAAA;AAAAC,YAAA,CAAA3F,EAAA;AAAA2F,YAAA,CAAA9E,GAAA;AAAA8E,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}